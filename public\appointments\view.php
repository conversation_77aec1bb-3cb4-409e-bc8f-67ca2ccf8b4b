<?php
include '../includes/config.php';

// Get appointment ID from URL
$appointment_id = $_GET['id'] ?? null;

if (!$appointment_id) {
    echo '<div class="text-center p-4"><p class="text-red-600">Invalid appointment ID.</p></div>';
    exit;
}

// Get appointment details with patient and staff information
$query = "SELECT a.*, 
                 p.first_name as patient_first_name, p.last_name as patient_last_name,
                 p.phone_number as patient_phone, p.email as patient_email,
                 s.first_name as staff_first_name, s.last_name as staff_last_name, 
                 s.role as staff_role, s.department as staff_department
          FROM appointments a
          JOIN patients p ON a.patient_id = p.patient_id
          JOIN staff s ON a.staff_id = s.staff_id
          WHERE a.id = ?";

$appointment = getSingleRecord($query, [$appointment_id]);

if (!$appointment) {
    echo '<div class="text-center p-4"><p class="text-red-600">Appointment not found.</p></div>';
    exit;
}

// Status color mapping
$status_colors = [
    'scheduled' => 'bg-blue-50 text-blue-800 border-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:border-blue-700',
    'completed' => 'bg-green-50 text-green-800 border-green-200 dark:bg-green-900 dark:text-green-300 dark:border-green-700',
    'cancelled' => 'bg-red-50 text-red-800 border-red-200 dark:bg-red-900 dark:text-red-300 dark:border-red-700',
    'no_show' => 'bg-yellow-50 text-yellow-800 border-yellow-200 dark:bg-yellow-900 dark:text-yellow-300 dark:border-yellow-700'
];
$status_class = $status_colors[$appointment['status']] ?? $status_colors['scheduled'];
?>

<div class="space-y-6">
    <!-- Appointment Header -->
    <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-2xl font-bold">Appointment Details</h2>
                <p class="text-blue-100">ID: <?php echo $appointment['id']; ?></p>
            </div>
            <div class="text-right">
                <div class="text-sm text-blue-100">Scheduled on</div>
                <div class="text-lg font-semibold"><?php echo date('M d, Y', strtotime($appointment['created_at'])); ?></div>
            </div>
        </div>
    </div>

    <!-- Main Information Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        
        <!-- Patient Information -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <i class="fas fa-user mr-2 text-blue-500"></i>
                Patient Information
            </h3>
            <div class="space-y-3">
                <div class="flex items-center">
                    <div class="w-12 h-12 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center shadow-lg mr-4">
                        <span class="text-lg font-bold text-white">
                            <?php echo strtoupper(substr($appointment['patient_first_name'], 0, 1) . substr($appointment['patient_last_name'], 0, 1)); ?>
                        </span>
                    </div>
                    <div>
                        <div class="font-medium text-gray-900 dark:text-white">
                            <?php echo htmlspecialchars($appointment['patient_first_name'] . ' ' . $appointment['patient_last_name']); ?>
                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">
                            Patient ID: <?php echo $appointment['patient_id']; ?>
                        </div>
                    </div>
                </div>
                
                <?php if ($appointment['patient_phone']): ?>
                <div class="flex justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Phone:</span>
                    <span class="text-gray-900 dark:text-white"><?php echo htmlspecialchars($appointment['patient_phone']); ?></span>
                </div>
                <?php endif; ?>
                
                <?php if ($appointment['patient_email']): ?>
                <div class="flex justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Email:</span>
                    <span class="text-gray-900 dark:text-white"><?php echo htmlspecialchars($appointment['patient_email']); ?></span>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Staff Information -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <i class="fas fa-user-md mr-2 text-green-500"></i>
                Staff Information
            </h3>
            <div class="space-y-3">
                <div class="flex items-center">
                    <div class="w-12 h-12 rounded-full bg-gradient-to-br from-green-400 to-blue-500 flex items-center justify-center shadow-lg mr-4">
                        <span class="text-lg font-bold text-white">
                            <?php echo strtoupper(substr($appointment['staff_first_name'], 0, 1) . substr($appointment['staff_last_name'], 0, 1)); ?>
                        </span>
                    </div>
                    <div>
                        <div class="font-medium text-gray-900 dark:text-white">
                            <?php echo htmlspecialchars($appointment['staff_first_name'] . ' ' . $appointment['staff_last_name']); ?>
                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">
                            Staff ID: <?php echo $appointment['staff_id']; ?>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Role:</span>
                    <span class="text-gray-900 dark:text-white"><?php echo htmlspecialchars($appointment['staff_role']); ?></span>
                </div>
                
                <?php if ($appointment['staff_department']): ?>
                <div class="flex justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Department:</span>
                    <span class="text-gray-900 dark:text-white"><?php echo htmlspecialchars($appointment['staff_department']); ?></span>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Appointment Details -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
            <i class="fas fa-calendar-alt mr-2 text-purple-500"></i>
            Appointment Details
        </h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
                <div class="flex justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Date:</span>
                    <span class="text-gray-900 dark:text-white font-medium">
                        <?php echo date('l, F j, Y', strtotime($appointment['appointment_date'])); ?>
                    </span>
                </div>
                
                <div class="flex justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Time:</span>
                    <span class="text-gray-900 dark:text-white font-medium">
                        <?php echo date('g:i A', strtotime($appointment['appointment_time'])); ?>
                    </span>
                </div>
                
                <div class="flex justify-between items-center">
                    <span class="text-gray-600 dark:text-gray-400">Status:</span>
                    <span class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-semibold border shadow-lg <?php echo $status_class; ?>">
                        <i class="fas fa-circle mr-2 text-xs"></i>
                        <?php echo ucfirst(str_replace('_', ' ', $appointment['status'])); ?>
                    </span>
                </div>
            </div>
            
            <div class="space-y-4">
                <div>
                    <span class="text-gray-600 dark:text-gray-400 block mb-2">Reason for Appointment:</span>
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                        <p class="text-gray-900 dark:text-white"><?php echo htmlspecialchars($appointment['reason']); ?></p>
                    </div>
                </div>
                
                <?php if ($appointment['notes']): ?>
                <div>
                    <span class="text-gray-600 dark:text-gray-400 block mb-2">Additional Notes:</span>
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                        <p class="text-gray-900 dark:text-white"><?php echo htmlspecialchars($appointment['notes']); ?></p>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Timestamps -->
    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">Record Information</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div class="flex justify-between">
                <span class="text-gray-600 dark:text-gray-400">Created:</span>
                <span class="text-gray-900 dark:text-white"><?php echo date('M d, Y g:i A', strtotime($appointment['created_at'])); ?></span>
            </div>
            <div class="flex justify-between">
                <span class="text-gray-600 dark:text-gray-400">Last Updated:</span>
                <span class="text-gray-900 dark:text-white"><?php echo date('M d, Y g:i A', strtotime($appointment['updated_at'])); ?></span>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600">
        <a href="edit.php?id=<?php echo $appointment['id']; ?>" class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            <i class="fas fa-edit mr-2"></i>
            Edit Appointment
        </a>
        <a href="../patients/view.php?id=<?php echo $appointment['patient_id']; ?>" class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-md shadow-sm hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
            <i class="fas fa-user mr-2"></i>
            View Patient
        </a>
        <a href="../staff/view.php?id=<?php echo $appointment['staff_id']; ?>" class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
            <i class="fas fa-user-md mr-2"></i>
            View Staff
        </a>
    </div>
</div>

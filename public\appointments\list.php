<?php
$pageTitle = "Appointment Records";
require_once '../includes/db_connection.php';

// Handle delete action
if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['id'])) {
    $appointment_id = (int)$_GET['id'];
    $delete_query = "DELETE FROM appointments WHERE id = ?";
    
    if (deleteRecord($delete_query, [$appointment_id])) {
        $success_message = "Appointment deleted successfully.";
    } else {
        $error_message = "Failed to delete appointment.";
    }
}

// Get search parameters
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$date_filter = $_GET['date'] ?? '';

// Build query with filters
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(p.first_name LIKE ? OR p.last_name LIKE ? OR s.first_name LIKE ? OR s.last_name LIKE ? OR a.reason LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param, $search_param]);
}

if (!empty($status_filter)) {
    $where_conditions[] = "a.status = ?";
    $params[] = $status_filter;
}

if (!empty($date_filter)) {
    $where_conditions[] = "a.appointment_date = ?";
    $params[] = $date_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get appointments with patient and staff information
$query = "SELECT a.*, 
                 p.first_name as patient_first_name, p.last_name as patient_last_name,
                 s.first_name as staff_first_name, s.last_name as staff_last_name, s.role as staff_role
          FROM appointments a
          JOIN patients p ON a.patient_id = p.patient_id
          JOIN staff s ON a.staff_id = s.staff_id
          $where_clause
          ORDER BY a.appointment_date DESC, a.appointment_time DESC";

$appointments = getAllRecords($query, $params);

// Get total count for statistics
$total_query = "SELECT COUNT(*) as total FROM appointments a
                JOIN patients p ON a.patient_id = p.patient_id
                JOIN staff s ON a.staff_id = s.staff_id
                $where_clause";
$total_result = getSingleRecord($total_query, $params);
$total_records = $total_result['total'] ?? 0;

include '../includes/header.php';
?>

<?php include '../includes/sidebar.php'; ?>

<div class="p-4 sm:ml-64">
    <div class="p-4 border-2 border-gray-200 border-dashed rounded-lg dark:border-gray-700 mt-14">
        
        <!-- Page Header -->
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Appointment Records</h1>
                <p class="text-gray-600 dark:text-gray-400">View and manage all scheduled appointments</p>
            </div>
            <div class="flex items-center space-x-4">
                <div class="bg-gradient-to-r from-green-500 to-green-600 text-black dark:text-white px-4 py-2 rounded-lg shadow-lg">
                    <div class="flex items-center">
                        <i class="fas fa-calendar-alt mr-2"></i>
                        <div>
                            <p class="text-xs opacity-90">Total Appointments</p>
                            <p class="text-lg font-bold"><?php echo number_format($total_records); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <?php if (isset($success_message)): ?>
            <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md dark:bg-green-900 dark:border-green-700 dark:text-green-300">
                <div class="flex">
                    <i class="fas fa-check-circle mr-2 mt-0.5"></i>
                    <span><?php echo htmlspecialchars($success_message); ?></span>
                </div>
            </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
            <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md dark:bg-red-900 dark:border-red-700 dark:text-red-300">
                <div class="flex">
                    <i class="fas fa-exclamation-circle mr-2 mt-0.5"></i>
                    <span><?php echo htmlspecialchars($error_message); ?></span>
                </div>
            </div>
        <?php endif; ?>

        <!-- Search and Filter Section -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
            <div class="p-6">
                <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Search</label>
                        <input type="text" name="search" id="search" 
                               value="<?php echo htmlspecialchars($search); ?>"
                               placeholder="Search patients, staff, or reason..."
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                    </div>
                    
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</label>
                        <select name="status" id="status" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                            <option value="">All Statuses</option>
                            <option value="scheduled" <?php echo $status_filter === 'scheduled' ? 'selected' : ''; ?>>Scheduled</option>
                            <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>Completed</option>
                            <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                            <option value="no_show" <?php echo $status_filter === 'no_show' ? 'selected' : ''; ?>>No Show</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Date</label>
                        <input type="date" name="date" id="date" 
                               value="<?php echo htmlspecialchars($date_filter); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                    </div>
                    
                    <div class="flex items-end space-x-2">
                        <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-search mr-2"></i>Filter
                        </button>
                        <a href="list.php" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-600">
                            <i class="fas fa-times mr-2"></i>Clear
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Appointments Table/Cards -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <?php if (empty($appointments)): ?>
                <div class="p-8 text-center">
                    <i class="fas fa-calendar-times text-4xl text-gray-400 dark:text-gray-600 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No appointments found</h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-4">
                        <?php if (!empty($search) || !empty($status_filter) || !empty($date_filter)): ?>
                            No appointments match your current filters. Try adjusting your search criteria.
                        <?php else: ?>
                            There are no appointments scheduled yet.
                        <?php endif; ?>
                    </p>
                    <a href="add.php" class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-calendar-plus mr-2"></i>
                        Schedule First Appointment
                    </a>
                </div>
            <?php else: ?>
                
                <!-- Desktop Table View -->
                <div class="appointment-table-responsive">
                    <div class="appointment-table-container">
                        <table class="appointment-table w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead>
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Patient</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Staff</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Date & Time</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Reason</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Scheduled</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <?php foreach ($appointments as $appointment): ?>
                                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                                        <!-- Patient -->
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-12 w-12">
                                                    <div class="h-12 w-12 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center shadow-lg">
                                                        <span class="text-lg font-bold text-white">
                                                            <?php echo strtoupper(substr($appointment['patient_first_name'], 0, 1) . substr($appointment['patient_last_name'], 0, 1)); ?>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                        <?php echo htmlspecialchars($appointment['patient_first_name'] . ' ' . $appointment['patient_last_name']); ?>
                                                    </div>
                                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                                        Patient ID: <?php echo $appointment['patient_id']; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>

                                        <!-- Staff -->
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                <?php echo htmlspecialchars($appointment['staff_first_name'] . ' ' . $appointment['staff_last_name']); ?>
                                            </div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                                <?php echo htmlspecialchars($appointment['staff_role']); ?>
                                            </div>
                                        </td>

                                        <!-- Date & Time -->
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                <?php echo date('M d, Y', strtotime($appointment['appointment_date'])); ?>
                                            </div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                                <?php echo date('g:i A', strtotime($appointment['appointment_time'])); ?>
                                            </div>
                                        </td>

                                        <!-- Reason -->
                                        <td class="px-6 py-4">
                                            <div class="text-sm text-gray-900 dark:text-white max-w-xs truncate" title="<?php echo htmlspecialchars($appointment['reason']); ?>">
                                                <?php echo htmlspecialchars($appointment['reason']); ?>
                                            </div>
                                        </td>

                                        <!-- Status -->
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <?php
                                            $status_colors = [
                                                'scheduled' => 'bg-blue-50 text-blue-800 border-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:border-blue-700',
                                                'completed' => 'bg-green-50 text-green-800 border-green-200 dark:bg-green-900 dark:text-green-300 dark:border-green-700',
                                                'cancelled' => 'bg-red-50 text-red-800 border-red-200 dark:bg-red-900 dark:text-red-300 dark:border-red-700',
                                                'no_show' => 'bg-yellow-50 text-yellow-800 border-yellow-200 dark:bg-yellow-900 dark:text-yellow-300 dark:border-yellow-700'
                                            ];
                                            $status_class = $status_colors[$appointment['status']] ?? $status_colors['scheduled'];
                                            ?>
                                            <span class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-semibold border shadow-lg <?php echo $status_class; ?>">
                                                <i class="fas fa-circle mr-2 text-xs"></i>
                                                <?php echo ucfirst(str_replace('_', ' ', $appointment['status'])); ?>
                                            </span>
                                        </td>

                                        <!-- Scheduled -->
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm">
                                                <div class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-gradient-to-r from-purple-500 to-purple-600 text-black dark:text-white shadow-lg">
                                                    <i class="fas fa-calendar-alt mr-2"></i>
                                                    <?php echo date('M d, Y', strtotime($appointment['created_at'])); ?>
                                                </div>
                                            </div>
                                        </td>

                                        <!-- Actions -->
                                        <td class="px-6 py-4 whitespace-nowrap text-right">
                                            <div class="flex items-center justify-end space-x-2">
                                                <button onclick="viewAppointment(<?php echo $appointment['id']; ?>)"
                                                        class="table-action-btn bg-blue-100 text-blue-600 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-400 dark:hover:bg-blue-800"
                                                        title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <a href="edit.php?id=<?php echo $appointment['id']; ?>"
                                                   class="table-action-btn bg-green-100 text-green-600 hover:bg-green-200 dark:bg-green-900 dark:text-green-400 dark:hover:bg-green-800"
                                                   title="Edit Appointment">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button onclick="deleteAppointment(<?php echo $appointment['id']; ?>, '<?php echo htmlspecialchars($appointment['patient_first_name'] . ' ' . $appointment['patient_last_name']); ?>')"
                                                        class="table-action-btn bg-red-100 text-red-600 hover:bg-red-200 dark:bg-red-900 dark:text-red-400 dark:hover:bg-red-800"
                                                        title="Delete Appointment">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Mobile Cards View -->
                <div class="appointment-cards-mobile">
                    <?php foreach ($appointments as $appointment): ?>
                        <div class="appointment-card">
                            <div class="appointment-card-header">
                                <div class="appointment-card-avatar">
                                    <?php echo strtoupper(substr($appointment['patient_first_name'], 0, 1) . substr($appointment['patient_last_name'], 0, 1)); ?>
                                </div>
                                <div class="appointment-card-info">
                                    <div class="appointment-card-name">
                                        <?php echo htmlspecialchars($appointment['patient_first_name'] . ' ' . $appointment['patient_last_name']); ?>
                                    </div>
                                    <div class="appointment-card-id">
                                        Appointment ID: <?php echo $appointment['id']; ?>
                                    </div>
                                </div>
                            </div>

                            <div class="appointment-card-content">
                                <div class="appointment-card-detail">
                                    <span class="appointment-card-label">Staff:</span>
                                    <span class="appointment-card-value"><?php echo htmlspecialchars($appointment['staff_first_name'] . ' ' . $appointment['staff_last_name'] . ' (' . $appointment['staff_role'] . ')'); ?></span>
                                </div>

                                <div class="appointment-card-detail">
                                    <span class="appointment-card-label">Date:</span>
                                    <span class="appointment-card-value"><?php echo date('M d, Y', strtotime($appointment['appointment_date'])); ?></span>
                                </div>

                                <div class="appointment-card-detail">
                                    <span class="appointment-card-label">Time:</span>
                                    <span class="appointment-card-value"><?php echo date('g:i A', strtotime($appointment['appointment_time'])); ?></span>
                                </div>

                                <div class="appointment-card-detail">
                                    <span class="appointment-card-label">Reason:</span>
                                    <span class="appointment-card-value"><?php echo htmlspecialchars($appointment['reason']); ?></span>
                                </div>

                                <div class="appointment-card-detail">
                                    <span class="appointment-card-label">Status:</span>
                                    <?php
                                    $status_colors = [
                                        'scheduled' => 'bg-blue-50 text-blue-800 border-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:border-blue-700',
                                        'completed' => 'bg-green-50 text-green-800 border-green-200 dark:bg-green-900 dark:text-green-300 dark:border-green-700',
                                        'cancelled' => 'bg-red-50 text-red-800 border-red-200 dark:bg-red-900 dark:text-red-300 dark:border-red-700',
                                        'no_show' => 'bg-yellow-50 text-yellow-800 border-yellow-200 dark:bg-yellow-900 dark:text-yellow-300 dark:border-yellow-700'
                                    ];
                                    $status_class = $status_colors[$appointment['status']] ?? $status_colors['scheduled'];
                                    ?>
                                    <span class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-semibold border shadow-lg <?php echo $status_class; ?>">
                                        <i class="fas fa-circle mr-2 text-xs"></i>
                                        <?php echo ucfirst(str_replace('_', ' ', $appointment['status'])); ?>
                                    </span>
                                </div>

                                <div class="appointment-card-detail">
                                    <span class="appointment-card-label">Scheduled:</span>
                                    <span class="appointment-card-value"><?php echo date('M d, Y', strtotime($appointment['created_at'])); ?></span>
                                </div>
                            </div>

                            <div class="appointment-card-actions">
                                <button onclick="viewAppointment(<?php echo $appointment['id']; ?>)"
                                        class="appointment-card-action-btn view"
                                        title="View Details">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <a href="edit.php?id=<?php echo $appointment['id']; ?>"
                                   class="appointment-card-action-btn edit"
                                   title="Edit Appointment">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button onclick="deleteAppointment(<?php echo $appointment['id']; ?>, '<?php echo htmlspecialchars($appointment['patient_first_name'] . ' ' . $appointment['patient_last_name']); ?>')"
                                        class="appointment-card-action-btn delete"
                                        title="Delete Appointment">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Appointment Details Modal -->
<div id="appointmentModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Appointment Details</h3>
                <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div id="appointmentDetails" class="text-sm text-gray-500 dark:text-gray-400">
                <!-- Appointment details will be loaded here -->
            </div>
        </div>
    </div>
</div>

<script>
function viewAppointment(appointmentId) {
    // Show modal with appointment details
    fetch(`view.php?id=${appointmentId}`)
        .then(response => response.text())
        .then(data => {
            document.getElementById('appointmentDetails').innerHTML = data;
            document.getElementById('appointmentModal').classList.remove('hidden');
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading appointment details');
        });
}

function closeModal() {
    document.getElementById('appointmentModal').classList.add('hidden');
}

function deleteAppointment(appointmentId, patientName) {
    if (confirm(`Are you sure you want to delete the appointment for ${patientName}? This action cannot be undone.`)) {
        window.location.href = `list.php?action=delete&id=${appointmentId}`;
    }
}
</script>

<?php include '../includes/footer.php'; ?>

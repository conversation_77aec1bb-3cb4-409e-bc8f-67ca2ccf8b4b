<?php
$pageTitle = "Appointment Scheduling";
include '../includes/config.php';

// Initialize variables
$errors = [];
$success_message = '';

// Get patient_id and staff_id from URL parameters if provided
$selected_patient_id = $_GET['patient_id'] ?? '';
$selected_staff_id = $_GET['staff_id'] ?? '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $patient_id = trim($_POST['patient_id'] ?? '');
    $staff_id = trim($_POST['staff_id'] ?? '');
    $appointment_date = trim($_POST['appointment_date'] ?? '');
    $appointment_time = trim($_POST['appointment_time'] ?? '');
    $reason = trim($_POST['reason'] ?? '');
    $notes = trim($_POST['notes'] ?? '');
    $status = trim($_POST['status'] ?? 'scheduled');

    // Validation
    if (empty($patient_id)) {
        $errors[] = "Patient selection is required.";
    }

    if (empty($staff_id)) {
        $errors[] = "Staff member selection is required.";
    }

    if (empty($appointment_date)) {
        $errors[] = "Appointment date is required.";
    } elseif (strtotime($appointment_date) < strtotime(date('Y-m-d'))) {
        $errors[] = "Appointment date cannot be in the past.";
    }

    if (empty($appointment_time)) {
        $errors[] = "Appointment time is required.";
    }

    if (empty($reason)) {
        $errors[] = "Reason for appointment is required.";
    }

    // Check for appointment conflicts
    if (empty($errors)) {
        $conflict_query = "SELECT id FROM appointments WHERE staff_id = ? AND appointment_date = ? AND appointment_time = ? AND status != 'cancelled'";
        $conflict_result = getSingleRecord($conflict_query, [$staff_id, $appointment_date, $appointment_time]);

        if ($conflict_result) {
            $errors[] = "The selected staff member already has an appointment at this date and time.";
        }
    }

    // If no errors, insert into database
    if (empty($errors)) {
        $query = "INSERT INTO appointments (patient_id, staff_id, appointment_date, appointment_time, reason, notes, status) VALUES (?, ?, ?, ?, ?, ?, ?)";

        $params = [
            $patient_id,
            $staff_id,
            $appointment_date,
            $appointment_time,
            $reason,
            $notes ?: null,
            $status
        ];

        if (insertRecord($query, $params)) {
            $success_message = "Appointment scheduled successfully!";
            // Clear form data
            $patient_id = $staff_id = $appointment_date = $appointment_time = $reason = $notes = '';
        } else {
            $errors[] = "Failed to schedule appointment. Please try again.";
        }
    }
}

// Get all patients for dropdown
$patients_query = "SELECT patient_id, first_name, last_name FROM patients ORDER BY first_name, last_name";
$patients = getAllRecords($patients_query);

// Get all staff members for dropdown (doctors and nurses)
$staff_query = "SELECT staff_id, first_name, last_name, role FROM staff WHERE status = 'Active' ORDER BY role, first_name, last_name";
$staff_members = getAllRecords($staff_query);

include '../includes/header.php';
?>

<?php include '../includes/sidebar.php'; ?>

<div class="p-4 sm:ml-64">
    <div class="p-4 border-2 border-gray-200 border-dashed rounded-lg dark:border-gray-700 mt-14">

        <!-- Page Header -->
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Appointment Scheduling</h1>
                <p class="text-gray-600 dark:text-gray-400">Schedule a new appointment for a patient</p>
            </div>
            <div class="flex items-center space-x-4">
                <!-- Records Button -->
                <a href="list.php" class="inline-flex items-center px-4 py-2 text-sm font-medium text-black dark:text-white bg-gradient-to-r from-purple-500 to-purple-600 border border-transparent rounded-md shadow-sm hover:from-purple-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                    <i class="fas fa-list mr-2"></i>
                    Records
                </a>
            </div>
        </div>

        <!-- Success Message -->
        <?php if ($success_message): ?>
            <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md dark:bg-green-900 dark:border-green-700 dark:text-green-300">
                <div class="flex">
                    <i class="fas fa-check-circle mr-2 mt-0.5"></i>
                    <span><?php echo htmlspecialchars($success_message); ?></span>
                </div>
            </div>
        <?php endif; ?>

        <!-- Error Messages -->
        <?php if (!empty($errors)): ?>
            <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md dark:bg-red-900 dark:border-red-700 dark:text-red-300">
                <div class="flex">
                    <i class="fas fa-exclamation-circle mr-2 mt-0.5"></i>
                    <div>
                        <p class="font-medium">Please correct the following errors:</p>
                        <ul class="mt-1 list-disc list-inside">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo htmlspecialchars($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Appointment Form -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                    <i class="fas fa-calendar-plus mr-2"></i>
                    Schedule New Appointment
                </h2>
            </div>

            <form method="POST" class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">

                    <!-- Patient Selection -->
                    <div>
                        <label for="patient_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Patient <span class="text-red-500">*</span>
                        </label>
                        <select name="patient_id" id="patient_id" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                            <option value="">Select a patient...</option>
                            <?php foreach ($patients as $patient): ?>
                                <option value="<?php echo $patient['patient_id']; ?>"
                                        <?php echo ($selected_patient_id == $patient['patient_id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($patient['first_name'] . ' ' . $patient['last_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Staff Selection -->
                    <div>
                        <label for="staff_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Doctor/Staff <span class="text-red-500">*</span>
                        </label>
                        <select name="staff_id" id="staff_id" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                            <option value="">Select a staff member...</option>
                            <?php foreach ($staff_members as $staff): ?>
                                <option value="<?php echo $staff['staff_id']; ?>"
                                        <?php echo ($selected_staff_id == $staff['staff_id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($staff['first_name'] . ' ' . $staff['last_name'] . ' (' . $staff['role'] . ')'); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Appointment Date -->
                    <div>
                        <label for="appointment_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Appointment Date <span class="text-red-500">*</span>
                        </label>
                        <input type="date" name="appointment_date" id="appointment_date" required
                               min="<?php echo date('Y-m-d'); ?>"
                               value="<?php echo htmlspecialchars($appointment_date ?? ''); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                    </div>

                    <!-- Appointment Time -->
                    <div>
                        <label for="appointment_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Appointment Time <span class="text-red-500">*</span>
                        </label>
                        <input type="time" name="appointment_time" id="appointment_time" required
                               value="<?php echo htmlspecialchars($appointment_time ?? ''); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                    </div>

                    <!-- Status -->
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Status
                        </label>
                        <select name="status" id="status"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                            <option value="scheduled" <?php echo ($status ?? 'scheduled') === 'scheduled' ? 'selected' : ''; ?>>Scheduled</option>
                            <option value="completed" <?php echo ($status ?? '') === 'completed' ? 'selected' : ''; ?>>Completed</option>
                            <option value="cancelled" <?php echo ($status ?? '') === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                            <option value="no_show" <?php echo ($status ?? '') === 'no_show' ? 'selected' : ''; ?>>No Show</option>
                        </select>
                    </div>
                </div>

                <!-- Reason for Appointment -->
                <div class="mt-6">
                    <label for="reason" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Reason for Appointment <span class="text-red-500">*</span>
                    </label>
                    <textarea name="reason" id="reason" rows="3" required
                              placeholder="Enter the reason for this appointment..."
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"><?php echo htmlspecialchars($reason ?? ''); ?></textarea>
                </div>

                <!-- Notes -->
                <div class="mt-6">
                    <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Additional Notes
                    </label>
                    <textarea name="notes" id="notes" rows="3"
                              placeholder="Enter any additional notes or special instructions..."
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"><?php echo htmlspecialchars($notes ?? ''); ?></textarea>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700 mt-6">
                    <a href="../dashboard.php" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-600">
                        Cancel
                    </a>
                    <button type="button" onclick="clearForm()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-600">
                        Clear Form
                    </button>
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-calendar-plus mr-2"></i>
                        Schedule Appointment
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function clearForm() {
    if (confirm('Are you sure you want to clear all form data?')) {
        document.querySelector('form').reset();
    }
}
</script>

<?php include '../includes/footer.php'; ?>
